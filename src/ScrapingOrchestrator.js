const ConfigurationManager = require('./core/ConfigurationManager');
const ErrorManager = require('./core/ErrorManager');
const Logger = require('./core/Logger');
const TokenLifecycleManager = require('./managers/TokenLifecycleManager');
const ParameterStateManager = require('./managers/ParameterStateManager');
const EnhancedPlaywrightCrawler = require('./scrapers/EnhancedPlaywrightCrawler');
const HealthMonitor = require('./monitoring/HealthMonitor');

/**
 * Main Scraping Orchestrator
 * Coordinates all components and manages the overall scraping workflow
 */
class ScrapingOrchestrator {
    constructor(configPath = './config.js', environment = 'development') {
        this.configPath = configPath;
        this.environment = environment;
        
        // Core components
        this.config = null;
        this.logger = null;
        this.errorManager = null;
        
        // Management components
        this.tokenManager = null;
        this.parameterManager = null;
        this.crawler = null;
        this.healthMonitor = null;
        
        // State management
        this.isInitialized = false;
        this.isRunning = false;
        this.isShuttingDown = false;
        this.currentBatch = [];
        this.processingStats = {
            totalProcessed: 0,
            successCount: 0,
            errorCount: 0,
            startTime: null,
            endTime: null
        };

        // Timers
        this.processingTimer = null;
        this.statusTimer = null;
    }

    /**
     * Initialize the orchestrator and all components
     */
    async initialize() {
        if (this.isInitialized) {
            throw new Error('Orchestrator is already initialized');
        }

        try {
            console.log('🚀 Initializing Scraping Orchestrator...');

            // Initialize core components
            await this.initializeCore();
            
            // Initialize management components
            await this.initializeManagers();
            
            // Initialize monitoring
            await this.initializeMonitoring();
            
            // Setup process handlers
            this.setupProcessHandlers();
            
            this.isInitialized = true;
            this.logger.info('✅ Scraping Orchestrator initialized successfully');
            
            return true;

        } catch (error) {
            console.error('❌ Failed to initialize Scraping Orchestrator:', error.message);
            await this.cleanup();
            throw error;
        }
    }

    /**
     * Initialize core components
     */
    async initializeCore() {
        // Configuration Manager
        this.config = new ConfigurationManager(this.configPath, this.environment);
        
        // Logger
        this.logger = new Logger({
            ...this.config.get('logging'),
            instanceId: this.config.get('app.instanceId')
        });
        
        // Error Manager
        this.errorManager = new ErrorManager({
            ...this.config.get('errors'),
            logger: this.logger
        });

        this.logger.info('✅ Core components initialized');
    }

    /**
     * Initialize management components
     */
    async initializeManagers() {
        // Token Lifecycle Manager
        this.tokenManager = new TokenLifecycleManager({
            ...this.config.get('tokens'),
            tokensFile: this.config.get('files.tokensFile'),
            instanceId: this.config.get('app.instanceId'),
            environment: this.config.get('app.environment'),
            ignoreTokenExpiration: this.config.get('ignoreTokenExpiration'),
            logger: this.logger,
            errorManager: this.errorManager
        });
        await this.tokenManager.initialize();

        // Parameter State Manager
        this.parameterManager = new ParameterStateManager({
            ...this.config.get('parameters'),
            paramsFile: this.config.get('files.paramsFile'),
            progressFile: this.config.get('files.progressFile'),
            instanceId: this.config.get('app.instanceId'),
            environment: this.config.get('app.environment'),
            logger: this.logger,
            errorManager: this.errorManager
        });
        await this.parameterManager.initialize();

        // Enhanced Playwright Crawler
        this.crawler = new EnhancedPlaywrightCrawler({
            ...this.config.get('browser'),
            ...this.config.get('scraping'),
            ...this.config.config,
            outputDir: this.config.get('files.outputDir'),
            instanceId: this.config.get('app.instanceId'),
            logger: this.logger,
            errorManager: this.errorManager,
            tokenManager: this.tokenManager
        });
        await this.crawler.initialize();

        this.logger.info('✅ Management components initialized');
    }

    /**
     * Initialize monitoring
     */
    async initializeMonitoring() {
        this.healthMonitor = new HealthMonitor({
            ...this.config.get('monitoring'),
            instanceId: this.config.get('app.instanceId'),
            logger: this.logger,
            errorManager: this.errorManager
        });

        this.healthMonitor.initialize({
            tokenManager: this.tokenManager,
            parameterManager: this.parameterManager,
            crawler: this.crawler
        });

        this.logger.info('✅ Monitoring initialized');
    }

    /**
     * Start the scraping process
     */
    async start() {
        if (!this.isInitialized) {
            throw new Error('Orchestrator must be initialized before starting');
        }

        if (this.isRunning) {
            this.logger.warn('⚠️ Scraping process is already running');
            return;
        }

        try {
            this.isRunning = true;
            this.processingStats.startTime = new Date();
            
            this.logger.info('🎯 Starting scraping process');
            
            // Start periodic status reporting
            this.startStatusReporting();
            
            // Start main processing loop
            await this.runProcessingLoop();
            
        } catch (error) {
            const enhancedError = this.errorManager.createEnhancedError(error, {
                operation: 'orchestrator_start'
            });
            
            this.logger.error('❌ Failed to start scraping process', { error: enhancedError.message });
            throw enhancedError;
        }
    }

    /**
     * Main processing loop
     */
    async runProcessingLoop() {
        while (this.isRunning && !this.isShuttingDown) {
            try {
                // Get next batch of parameters to process
                const batch = await this.parameterManager.getNextBatch();
                
                if (batch.length === 0) {
                    this.logger.info('📋 No more parameters to process, checking for completion...');
                    
                    // Check if all parameters are completed
                    const stats = this.parameterManager.getStatistics();
                    if (stats.pending === 0 && stats.processing === 0 && stats.retry === 0) {
                        this.logger.info('🎉 All parameters processed successfully!');
                        break;
                    }
                    
                    // Wait before checking again
                    await this.sleep(30000); // 30 seconds
                    continue;
                }

                this.currentBatch = batch;
                this.logger.info('📦 Processing batch', { 
                    batchSize: batch.length,
                    remaining: this.parameterManager.getPendingCount()
                });

                // Process batch
                await this.processBatch(batch);
                
                // Clear current batch
                this.currentBatch = [];
                
                // Brief pause between batches
                await this.sleep(1000);

            } catch (error) {
                const enhancedError = this.errorManager.createEnhancedError(error, {
                    operation: 'processing_loop'
                });
                
                this.logger.error('❌ Error in processing loop', { error: enhancedError.message });
                
                // Check if error is retryable
                if (enhancedError.retryable) {
                    const delay = this.errorManager.calculateRetryDelay(1, enhancedError.category);
                    this.logger.info(`⏳ Retrying in ${delay}ms...`);
                    await this.sleep(delay);
                } else {
                    this.logger.error('💥 Non-retryable error, stopping processing loop');
                    break;
                }
            }
        }

        this.processingStats.endTime = new Date();
        this.isRunning = false;
        
        this.logger.info('🏁 Processing loop completed', {
            duration: this.processingStats.endTime - this.processingStats.startTime,
            totalProcessed: this.processingStats.totalProcessed,
            successCount: this.processingStats.successCount,
            errorCount: this.processingStats.errorCount
        });
    }

    /**
     * Process a batch of parameters (sequential processing following original pattern)
     * Each parameter gets its own browser instance to simulate real user behavior
     */
    async processBatch(batch) {
        // Process parameters sequentially (one at a time) to follow original pattern
        // This ensures each parameter gets its own browser instance with proper token injection
        const results = [];

        for (let i = 0; i < batch.length; i++) {
            const param = batch[i];

            try {
                this.logger.info(`🎯 Processing parameter ${i + 1}/${batch.length}`, { param: param.key });

                const result = await this.processParameter(param);
                results.push({ status: 'fulfilled', value: result });

                this.processingStats.successCount++;
                await this.parameterManager.markParameterCompleted(param.key, result);

                this.logger.info(`✅ Parameter completed`, {
                    param: param.key,
                    dataSize: result.totalCount || 0,
                    pagesProcessed: result.pagesProcessed || 0
                });

            } catch (error) {
                results.push({ status: 'rejected', reason: error });

                this.processingStats.errorCount++;
                await this.parameterManager.markParameterFailed(param.key, error);

                this.logger.error(`❌ Parameter failed`, {
                    param: param.key,
                    error: error.message
                });
            }

            this.processingStats.totalProcessed++;
        }

        // Analyze results
        let successCount = 0;
        let errorCount = 0;

        for (const result of results) {
            if (result.status === 'fulfilled') {
                successCount++;
            } else {
                errorCount++;
            }
        }

        this.logger.info('📊 Batch processed', {
            batchSize: batch.length,
            successCount,
            errorCount,
            totalProcessed: this.processingStats.totalProcessed
        });
    }

    /**
     * Process individual parameter (following original PlaywrightCrawler pattern)
     * Each parameter gets its own browser instance with token injection
     */
    async processParameter(param) {
        const operationId = this.logger.startOperation('process_parameter', { paramKey: param.key });

        try {
            this.logger.debug('🔄 Processing parameter', { param });

            // Use the new single parameter processing method (following original pattern)
            const result = await this.crawler.processSingleParameter(param);

            this.logger.completeOperation('process_parameter', operationId, {
                paramKey: param.key,
                dataSize: result.totalCount || 0,
                pagesProcessed: result.pagesProcessed || 0,
                savedPath: result.savedPath
            });

            return result;

        } catch (error) {
            const enhancedError = this.errorManager.createEnhancedError(error, {
                operation: 'process_parameter',
                paramKey: param.key
            });

            this.logger.failOperation('process_parameter', operationId, enhancedError, {
                paramKey: param.key
            });

            throw enhancedError;
        }
    }

    /**
     * Start periodic status reporting
     */
    startStatusReporting() {
        this.statusTimer = setInterval(() => {
            this.reportStatus();
        }, 60000); // Every minute
    }

    /**
     * Report current status
     */
    reportStatus() {
        try {
            const paramStats = this.parameterManager.getStatistics();
            const tokenStats = this.tokenManager.getStatistics();
            const crawlerStats = this.crawler.getStatistics();
            const healthStatus = this.healthMonitor.getCurrentHealth();
            
            this.logger.info('📊 Status Report', {
                parameters: {
                    total: paramStats.total,
                    completed: paramStats.completed,
                    pending: paramStats.pending,
                    processing: paramStats.processing,
                    failed: paramStats.failed,
                    completionRate: paramStats.completionRate.toFixed(1) + '%'
                },
                tokens: {
                    total: tokenStats.totalTokens,
                    valid: tokenStats.validTokens,
                    healthy: tokenStats.healthyTokens
                },
                crawler: {
                    successRate: crawlerStats.successRate.toFixed(1) + '%',
                    averageResponseTime: Math.round(crawlerStats.averageResponseTime) + 'ms'
                },
                health: {
                    overall: healthStatus.overallHealth,
                    alerts: healthStatus.recentAlerts.length
                },
                processing: {
                    totalProcessed: this.processingStats.totalProcessed,
                    successCount: this.processingStats.successCount,
                    errorCount: this.processingStats.errorCount,
                    currentBatchSize: this.currentBatch.length
                }
            });

        } catch (error) {
            this.logger.error('❌ Failed to generate status report', { error: error.message });
        }
    }

    /**
     * Get comprehensive status
     */
    getStatus() {
        return {
            orchestrator: {
                isInitialized: this.isInitialized,
                isRunning: this.isRunning,
                isShuttingDown: this.isShuttingDown,
                processingStats: this.processingStats,
                currentBatchSize: this.currentBatch.length
            },
            parameters: this.parameterManager?.getStatistics(),
            tokens: this.tokenManager?.getStatistics(),
            crawler: this.crawler?.getStatistics(),
            health: this.healthMonitor?.getCurrentHealth(),
            errors: this.errorManager?.getErrorStatistics()
        };
    }

    /**
     * Stop the scraping process
     */
    async stop() {
        if (!this.isRunning) {
            this.logger.warn('⚠️ Scraping process is not running');
            return;
        }

        this.logger.info('🛑 Stopping scraping process...');
        this.isRunning = false;

        // Wait for current batch to complete (with timeout)
        let waitTime = 0;
        const maxWaitTime = 60000; // 1 minute
        
        while (this.currentBatch.length > 0 && waitTime < maxWaitTime) {
            await this.sleep(1000);
            waitTime += 1000;
        }

        // Clear timers
        if (this.statusTimer) {
            clearInterval(this.statusTimer);
            this.statusTimer = null;
        }

        this.logger.info('✅ Scraping process stopped');
    }

    /**
     * Setup process handlers for graceful shutdown
     */
    setupProcessHandlers() {
        const gracefulShutdown = async (signal) => {
            this.logger.info(`🔄 Received ${signal}, initiating graceful shutdown...`);
            await this.shutdown();
            process.exit(0);
        };

        process.on('SIGINT', gracefulShutdown);
        process.on('SIGTERM', gracefulShutdown);
        
        process.on('uncaughtException', (error) => {
            this.logger.fatal('💥 Uncaught Exception', { error: error.message, stack: error.stack });
            this.shutdown().then(() => process.exit(1));
        });

        process.on('unhandledRejection', (reason, promise) => {
            this.logger.fatal('💥 Unhandled Rejection', { reason, promise });
            this.shutdown().then(() => process.exit(1));
        });
    }

    /**
     * Shutdown orchestrator and all components
     */
    async shutdown() {
        if (this.isShuttingDown) return;
        this.isShuttingDown = true;

        this.logger?.info('🔄 Shutting down Scraping Orchestrator...');

        try {
            // Stop processing
            await this.stop();

            // Shutdown components in reverse order
            if (this.healthMonitor) {
                this.healthMonitor.shutdown();
            }

            if (this.crawler) {
                await this.crawler.shutdown();
            }

            if (this.parameterManager) {
                await this.parameterManager.shutdown();
            }

            if (this.tokenManager) {
                await this.tokenManager.shutdown();
            }

            // Final cleanup
            await this.cleanup();

            this.logger?.info('✅ Scraping Orchestrator shutdown completed');

        } catch (error) {
            console.error('❌ Error during shutdown:', error.message);
        }
    }

    /**
     * Cleanup resources
     */
    async cleanup() {
        // Clear any remaining timers
        if (this.processingTimer) {
            clearInterval(this.processingTimer);
            this.processingTimer = null;
        }

        if (this.statusTimer) {
            clearInterval(this.statusTimer);
            this.statusTimer = null;
        }

        // Reset state
        this.isInitialized = false;
        this.isRunning = false;
        this.currentBatch = [];
    }

    /**
     * Sleep utility
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = ScrapingOrchestrator;
